"""
Configuration settings for the video automation system.
"""
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Application configuration class."""
    
    # Reddit API settings
    REDDIT_CLIENT_ID = os.getenv('REDDIT_CLIENT_ID')
    REDDIT_CLIENT_SECRET = os.getenv('REDDIT_CLIENT_SECRET')
    REDDIT_USER_AGENT = os.getenv('REDDIT_USER_AGENT', 'VideoAutomation/1.0')
    
    # OpenAI settings
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    
    # Google Cloud TTS settings
    GOOGLE_APPLICATION_CREDENTIALS = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
    
    # ElevenLabs settings
    ELEVENLABS_API_KEY = os.getenv('ELEVENLABS_API_KEY')
    
    # Application settings
    DEFAULT_SUBREDDIT = os.getenv('DEFAULT_SUBREDDIT', 'AskReddit')
    DEFAULT_CHARACTER_LIMIT = int(os.getenv('DEFAULT_CHARACTER_LIMIT', '500'))
    
    # Directory settings
    OUTPUT_DIRECTORY = Path(os.getenv('OUTPUT_DIRECTORY', './output'))
    BACKGROUND_VIDEOS_DIRECTORY = Path(os.getenv('BACKGROUND_VIDEOS_DIRECTORY', './background_videos'))
    
    # TTS settings
    SUPPORTED_LANGUAGES = ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh']
    DEFAULT_VOICE_SPEED = 1.0
    
    # Video settings
    DEFAULT_VIDEO_RESOLUTION = (1920, 1080)
    DEFAULT_FPS = 30
    DEFAULT_AUDIO_BITRATE = '128k'
    DEFAULT_VIDEO_BITRATE = '2M'
    
    @classmethod
    def validate_config(cls):
        """Validate that required configuration is present."""
        required_vars = [
            'REDDIT_CLIENT_ID',
            'REDDIT_CLIENT_SECRET', 
            'OPENAI_API_KEY'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not getattr(cls, var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
        
        # Create directories if they don't exist
        cls.OUTPUT_DIRECTORY.mkdir(exist_ok=True)
        cls.BACKGROUND_VIDEOS_DIRECTORY.mkdir(exist_ok=True)
        
        return True
