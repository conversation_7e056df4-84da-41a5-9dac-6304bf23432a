"""
Simple verification script to check if the project is set up correctly.
This script doesn't require external dependencies.
"""
import os
import sys
from pathlib import Path

def check_file_structure():
    """Check if all required files and directories exist."""
    print("🔍 Checking file structure...")
    
    required_files = [
        'README.md',
        'requirements.txt',
        'config.py',
        '.env.example',
        'src/main.py',
        'src/__init__.py',
        'src/modules/__init__.py'
    ]
    
    required_modules = [
        'src/modules/reddit_scraper.py',
        'src/modules/text_processor.py',
        'src/modules/tts_generator.py',
        'src/modules/subtitle_generator.py',
        'src/modules/background_video_manager.py',
        'src/modules/video_composer.py',
        'src/modules/download_manager.py'
    ]
    
    required_dirs = [
        'src',
        'src/modules',
        'output',
        'background_videos',
        'tests'
    ]
    
    all_files = required_files + required_modules
    
    missing_files = []
    missing_dirs = []
    
    # Check files
    for file_path in all_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    # Check directories
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            missing_dirs.append(dir_path)
    
    if missing_files:
        print("❌ Missing files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
    
    if missing_dirs:
        print("❌ Missing directories:")
        for dir_path in missing_dirs:
            print(f"   - {dir_path}")
    
    if not missing_files and not missing_dirs:
        print("✅ All required files and directories present")
        return True
    
    return False

def check_python_syntax():
    """Check if Python files have valid syntax."""
    print("\n🐍 Checking Python syntax...")
    
    python_files = [
        'config.py',
        'src/main.py',
        'src/modules/reddit_scraper.py',
        'src/modules/text_processor.py',
        'src/modules/tts_generator.py',
        'src/modules/subtitle_generator.py',
        'src/modules/background_video_manager.py',
        'src/modules/video_composer.py',
        'src/modules/download_manager.py'
    ]
    
    syntax_errors = []
    
    for file_path in python_files:
        if Path(file_path).exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                compile(content, file_path, 'exec')
            except SyntaxError as e:
                syntax_errors.append(f"{file_path}: {e}")
            except Exception as e:
                # Other errors (like import errors) are expected
                pass
    
    if syntax_errors:
        print("❌ Syntax errors found:")
        for error in syntax_errors:
            print(f"   - {error}")
        return False
    else:
        print("✅ All Python files have valid syntax")
        return True

def check_requirements():
    """Check requirements.txt content."""
    print("\n📦 Checking requirements...")
    
    if not Path('requirements.txt').exists():
        print("❌ requirements.txt not found")
        return False
    
    with open('requirements.txt', 'r') as f:
        requirements = f.read().strip()
    
    expected_packages = [
        'praw',
        'openai',
        'requests',
        'python-dotenv',
        'google-cloud-texttospeech',
        'elevenlabs',
        'ffmpeg-python',
        'flask'
    ]
    
    missing_packages = []
    for package in expected_packages:
        if package not in requirements:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing packages in requirements.txt:")
        for package in missing_packages:
            print(f"   - {package}")
        return False
    else:
        print("✅ All expected packages found in requirements.txt")
        return True

def check_config():
    """Check config.py structure."""
    print("\n⚙️ Checking configuration...")
    
    try:
        # Add src to path temporarily
        sys.path.insert(0, 'src')
        
        # Try to import config (this might fail due to missing .env, which is expected)
        try:
            import config
            print("✅ Config module can be imported")
            
            # Check if Config class exists
            if hasattr(config, 'Config'):
                print("✅ Config class found")
                
                # Check for expected attributes
                expected_attrs = [
                    'DEFAULT_SUBREDDIT',
                    'DEFAULT_CHARACTER_LIMIT',
                    'OUTPUT_DIRECTORY',
                    'SUPPORTED_LANGUAGES'
                ]
                
                missing_attrs = []
                for attr in expected_attrs:
                    if not hasattr(config.Config, attr):
                        missing_attrs.append(attr)
                
                if missing_attrs:
                    print("❌ Missing Config attributes:")
                    for attr in missing_attrs:
                        print(f"   - {attr}")
                    return False
                else:
                    print("✅ All expected Config attributes found")
                    return True
            else:
                print("❌ Config class not found")
                return False
                
        except Exception as e:
            print(f"⚠️ Config import failed (expected if .env not configured): {e}")
            return True  # This is expected
            
    except Exception as e:
        print(f"❌ Error checking config: {e}")
        return False
    finally:
        # Remove src from path
        if 'src' in sys.path:
            sys.path.remove('src')

def check_example_files():
    """Check example and utility files."""
    print("\n📝 Checking example files...")
    
    example_files = [
        'example_usage.py',
        'tests/test_basic_functionality.py'
    ]
    
    missing_examples = []
    for file_path in example_files:
        if not Path(file_path).exists():
            missing_examples.append(file_path)
    
    if missing_examples:
        print("❌ Missing example files:")
        for file_path in missing_examples:
            print(f"   - {file_path}")
        return False
    else:
        print("✅ All example files present")
        return True

def main():
    """Run all verification checks."""
    print("🎬 Video Automation System - Setup Verification")
    print("=" * 60)
    
    checks = [
        ("File Structure", check_file_structure),
        ("Python Syntax", check_python_syntax),
        ("Requirements", check_requirements),
        ("Configuration", check_config),
        ("Example Files", check_example_files)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ Error in {check_name} check: {e}")
            results.append((check_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{check_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} checks passed")
    
    if passed == total:
        print("\n🎉 All checks passed! The project is set up correctly.")
        print("\nNext steps:")
        print("1. Copy .env.example to .env and configure your API keys")
        print("2. Install dependencies: pip install -r requirements.txt")
        print("3. Add background videos to the background_videos/ directory")
        print("4. Run: python example_usage.py")
    else:
        print(f"\n⚠️ {total - passed} checks failed. Please fix the issues above.")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
